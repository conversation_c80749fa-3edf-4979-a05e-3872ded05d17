from faker.typing import Country

from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "السبت",
        "1": "الأحد",
        "2": "الإثنين",
        "3": "الثلاثاء",
        "4": "الأربعاء",
        "5": "الخميس",
        "6": "الجمعة",
    }

    MONTH_NAMES = {
        "01": "كانون الثّاني",
        "02": "شباط",
        "03": "آذار",
        "04": "نيسان",
        "05": "أيّار",
        "06": "حزيران",
        "07": "تمّوز",
        "08": "آب",
        "09": "أيلول",
        "10": "تشرين الأول",
        "11": "تشرين الثاني",
        "12": "كانون الأول",
    }

    centuries = [
        "الأول",
        "الثاني",
        "الثالث",
        "الرابع",
        "الخامس",
        "السادس",
        "السابع",
        "الثامن",
        "التاسع",
        "العاشر",
        "الحادي عشر",
        "الثاني عشر",
        "الثالث عشر",
        "الرابع عشر",
        "الخامس عشر",
        "السادس عشر",
        "الثامن عشر",
        "التاسع عشر",
        "العشرين",
        "الحادي والعشرين",
        "الثاني والعشرين",
    ]

    countries = [
        Country(
            timezones=["أوروب/أندورا"],
            alpha_2_code="AD",
            alpha_3_code="AND",
            continent="أوروبا",
            name="أندورا",
            capital="أندورا لا فيلا",
        ),
        Country(
            timezones=["آسيا/كابل"],
            alpha_2_code="AF",
            alpha_3_code="AFG",
            continent="آسيا",
            name="أفغانستان",
            capital="كابل",
        ),
        Country(
            timezones=["أمريكا/أنتيغوا"],
            alpha_2_code="AG",
            alpha_3_code="ATG",
            continent="أمريكا الشمالية",
            name="أنتيغوا وباربودا",
            capital="سانت جونز",
        ),
        Country(
            timezones=["أوروبا/تيرانا"],
            alpha_2_code="AL",
            alpha_3_code="ALB",
            continent="أوروبا",
            name="ألبانيا",
            capital="تيرانا",
        ),
        Country(
            timezones=["آسيا/يريفان"],
            alpha_2_code="AM",
            alpha_3_code="ARM",
            continent="آسيا",
            name="أرمينيا",
            capital="يريفان",
        ),
        Country(
            timezones=["إفريقيا/لواندا"],
            alpha_2_code="AO",
            alpha_3_code="AGO",
            continent="إفريقيا",
            name="أنغولا",
            capital="لواندا",
        ),
        Country(
            timezones=[
                "أمريكا/الأرجنتين/بوينس_آيرس",
                "أمريكا/الأرجنتين/Cordoba",
                "أمريكا/الأرجنتين/خوخوي",
                "أمريكا/الأرجنتين/توكومان",
                "أمريكا/الأرجنتين/كاتاماركا",
                "أمريكا/الأرجنتين/لا_ريوخا",
                "أمريكا/الأرجنتين/سان_خوان",
                "أمريكا/الأرجنتين/مندوزا",
                "أمريكا/الأرجنتين/ريو_غاليغوس",
                "أمريكا/الأرجنتين/أوشوايا",
            ],
            alpha_2_code="AR",
            alpha_3_code="ARG",
            continent="أمريكا الجنوبية",
            name="الأرجنتين",
            capital="بوينس آيرس",
        ),
        Country(
            timezones=["أوروبا/النمسا"],
            alpha_2_code="AT",
            alpha_3_code="AUT",
            continent="أوروبا",
            name="النمسا",
            capital="فيينا",
        ),
        Country(
            timezones=[
                "أستراليا/لورد_هاو",
                "أستراليا/هوبارت",
                "أستراليا/كري",
                "أستراليا/ملبورن",
                "أستراليا/سدني",
                "أستراليا/بروكن_هل",
                "أستراليا/بريزبن",
                "أستراليا/ليندمان",
                "أستراليا/أديلايد",
                "أستراليا/داروين",
                "أستراليا/برث",
            ],
            alpha_2_code="AU",
            alpha_3_code="AUS",
            continent="أوقيانوسيا",
            name="أستراليا",
            capital="كانبرا",
        ),
        Country(
            timezones=["آسيا/باكو"],
            alpha_2_code="AZ",
            alpha_3_code="AZE",
            continent="آسيا",
            name="أذربيجان",
            capital="باكو",
        ),
        Country(
            timezones=["أمريكا/باربادوس"],
            alpha_2_code="BB",
            alpha_3_code="BRB",
            continent="أمريكا الشمالية",
            name="باربادوس",
            capital="بريدج تاون",
        ),
        Country(
            timezones=["آسيا/دكا"],
            alpha_2_code="BD",
            alpha_3_code="BGD",
            continent="آسيا",
            name="بنغلادش",
            capital="دكا",
        ),
        Country(
            timezones=["أوروبا/بروكسل"],
            alpha_2_code="BE",
            alpha_3_code="BEL",
            continent="أوروبا",
            name="بلجيكا",
            capital="بروكسل",
        ),
        Country(
            timezones=["إفريقيا/واغادوغو"],
            alpha_2_code="BF",
            alpha_3_code="BFA",
            continent="إفريقيا",
            name="بوركينا فاسو",
            capital="واغادوغو",
        ),
        Country(
            timezones=["أوروبا/صوفيا"],
            alpha_2_code="BG",
            alpha_3_code="BGR",
            continent="أوروبا",
            name="بلغاريا",
            capital="صوفيا",
        ),
        Country(
            timezones=["آسيا/البحرين"],
            alpha_2_code="BH",
            alpha_3_code="BHR",
            continent="آسيا",
            name="البحرين",
            capital="المنامة",
        ),
        Country(
            timezones=["إفريقيا/بوجمبورا"],
            alpha_2_code="BI",
            alpha_3_code="BDI",
            continent="إفريقيا",
            name="بوروندي",
            capital="بوجمبورا",
        ),
        Country(
            timezones=["إفريقيا/بورتو نوفو"],
            alpha_2_code="BJ",
            alpha_3_code="BEN",
            continent="إفريقيا",
            name="بنين",
            capital="بورتو نوفو",
        ),
        Country(
            timezones=["آسيا/بروناي"],
            alpha_2_code="BN",
            alpha_3_code="BRN",
            continent="آسيا",
            name="اتحاد بروناي (دار السلام)",
            capital="بندر سري بكاوان",
        ),
        Country(
            timezones=["أمريكا/لاباز"],
            alpha_2_code="BO",
            alpha_3_code="BOL",
            continent="أمريكا الجنوبية",
            name="بوليفيا",
            capital="سوكري",
        ),
        Country(
            timezones=[
                "أمريكا/نورونها",
                "أمريكا/بليم",
                "أمريكا/فورتاليزا",
                "أمريكا/ريسيفي",
                "أمريكا/أراغوينا",
                "أمريكا/ماسايو",
                "أمريكا/باهيا",
                "أمريكا/ساو_باولو",
                "أمريكا/كامبو_غراندي",
                "أمريكا/كويابا",
                "أمريكا/بورتو_فاليو",
                "أمريكا/بوا_فيستا",
                "أمريكا/ماناوس",
                "أمريكا/إيرونيبي",
                "أمريكا/ريو_برانكو",
            ],
            alpha_2_code="BR",
            alpha_3_code="BRA",
            continent="أمريكا الجنوبية",
            name="البرازيل",
            capital="برازيليا",
        ),
        Country(
            timezones=["أمريكا/ناساو"],
            alpha_2_code="BS",
            alpha_3_code="BHS",
            continent="أمريكا الشمالية",
            name="باهاماس",
            capital="ناساو",
        ),
        Country(
            timezones=["آسيا/تيمفو"],
            alpha_2_code="BT",
            alpha_3_code="BTN",
            continent="آسيا",
            name="بوتان",
            capital="تيمفو",
        ),
        Country(
            timezones=["إفريقيا/غابورون"],
            alpha_2_code="BW",
            alpha_3_code="BWA",
            continent="إفريقيا",
            name="بوتسوانا",
            capital="غابورون",
        ),
        Country(
            timezones=["أوروبا/مينسك"],
            alpha_2_code="BY",
            alpha_3_code="BLR",
            continent="أوروبا",
            name="روسيا البيضاء",
            capital="مينسك",
        ),
        Country(
            timezones=["أمريكا/بليز"],
            alpha_2_code="BZ",
            alpha_3_code="BLZ",
            continent="أمريكا الشمالية",
            name="بليز",
            capital="بلموبان",
        ),
        Country(
            timezones=[
                "أمريكا/سينت_جونز",
                "أمريكا/هاليفاكس",
                "أمريكا/جليس_باي",
                "أمريكا/مونكتون",
                "أمريكا/جووس_باي",
                "أمريكا/بلانك_سابلون",
                "أمريكا/مونتريال",
                "أمريكا/تورونتو",
                "أمريكا/نيبيغون",
                "أمريكا/ثاندر_باي",
                "أمريكا/بانغيرتانغ",
                "أمريكا/إيكواليوت",
                "أمريكا/أتيكوكان",
                "أمريكا/رانكن_إنلت",
                "أمريكا/وينيبيغ",
                "أمريكا/رايني_ريفر",
                "أمريكا/كامبريدج_باي",
                "أمريكا/ريجينا",
                "أمريكا/سويفت_كارنت",
                "أمريكا/إدمونتون",
                "أمريكا/يلو_نايف",
                "أمريكا/إنوفك",
                "أمريكا/دوسن_كريك",
                "أمريكا/فانكوفر",
                "أمريكا/وايت_هورس",
                "أمريكا/داوسون",
            ],
            alpha_2_code="CA",
            alpha_3_code="CAN",
            continent="أمريكا الشمالية",
            name="كندا",
            capital="أوتاوا",
        ),
        Country(
            timezones=["إفريقيا/كينشاسا", "إفريقيا/لوبومباشي"],
            alpha_2_code="CD",
            alpha_3_code="COD",
            continent="إفريقيا",
            name="جمهورية الكونغو الديمقراطية",
            capital="كينشاسا",
        ),
        Country(
            timezones=["إفريقيا/برازافيل"],
            alpha_2_code="CG",
            alpha_3_code="COG",
            continent="إفريقيا",
            name="جمهورية الكونغو",
            capital="برازافيل",
        ),
        Country(
            timezones=["إفريقيا/أبيدجان"],
            alpha_2_code="CI",
            alpha_3_code="CIV",
            continent="إفريقيا",
            name="ساحل العاج",
            capital="ياموسوكرو",
        ),
        Country(
            timezones=["أمريكا/سانتياغو", "المحيط_الهاديء/جزيرة_القيامة"],
            alpha_2_code="CL",
            alpha_3_code="CHL",
            continent="أمريكا الجنوبية",
            name="تشيلي",
            capital="سانتياغو",
        ),
        Country(
            timezones=["إفريقيا/دوالا"],
            alpha_2_code="CM",
            alpha_3_code="CMR",
            continent="إفريقيا",
            name="الكاميرون",
            capital="ياوندي",
        ),
        Country(
            timezones=[
                "آسيا/شانغهاي",
                "آسيا/هاربن",
                "آسيا/تشونغتشينغ",
                "آسيا/أورومتشي",
                "آسيا/كاشغر",
            ],
            alpha_2_code="CN",
            alpha_3_code="CHN",
            continent="آسيا",
            name="جمهورية الصين الشعبية",
            capital="بكين",
        ),
        Country(
            timezones=["أمريكا/بوغوتا"],
            alpha_2_code="CO",
            alpha_3_code="COL",
            continent="أمريكا الجنوبية",
            name="كولومبيا",
            capital="بوغوتا",
        ),
        Country(
            timezones=["أمريكا/كوستاريكا"],
            alpha_2_code="CR",
            alpha_3_code="CRI",
            continent="أمريكا الشمالية",
            name="كوستاريكا",
            capital="سان خوسيه",
        ),
        Country(
            timezones=["أمريكا/هافانا"],
            alpha_2_code="CU",
            alpha_3_code="CUB",
            continent="أمريكا الشمالية",
            name="كوبا",
            capital="هافانا",
        ),
        Country(
            timezones=["الأطلنطي/الرأس_الأخضر"],
            alpha_2_code="CV",
            alpha_3_code="CPV",
            continent="إفريقيا",
            name="جمهورية الرأس الأخضر",
            capital="برايا",
        ),
        Country(
            timezones=["آسيا/نيقوسيا"],
            alpha_2_code="CY",
            alpha_3_code="CYP",
            continent="آسيا",
            name="قبرص",
            capital="نيقوسيا",
        ),
        Country(
            timezones=["أوروبا/براغ"],
            alpha_2_code="CZ",
            alpha_3_code="CZE",
            continent="أوروبا",
            name="جمهورية التشيك",
            capital="براغ",
        ),
        Country(
            timezones=["أوروبا/برلين"],
            alpha_2_code="DE",
            alpha_3_code="DEU",
            continent="أوروبا",
            name="ألمانيا",
            capital="برلين",
        ),
        Country(
            timezones=["إفريقيا/جيبوتي"],
            alpha_2_code="DJ",
            alpha_3_code="DJI",
            continent="إفريقيا",
            name="جيبوتي",
            capital="جيبوتي",
        ),
        Country(
            timezones=["أوروبا/كوبنهاغن"],
            alpha_2_code="DK",
            alpha_3_code="DNK",
            continent="أوروبا",
            name="الدنمارك",
            capital="كوبنهاغن",
        ),
        Country(
            timezones=["أمريكا/دومينيكا"],
            alpha_2_code="DM",
            alpha_3_code="DMA",
            continent="أمريكا الشمالية",
            name="دومينيكا",
            capital="روسياو",
        ),
        Country(
            timezones=["أمريكا/سانتو_دومينغو"],
            alpha_2_code="DO",
            alpha_3_code="DOM",
            continent="أمريكا الشمالية",
            name="جمهورية الدومينيكان",
            capital="سانتو دومينغو",
        ),
        Country(
            timezones=["أمريكا/غواياكيل", "المحيط_الهاديء/أرخبيل_غالاباغوس"],
            alpha_2_code="EC",
            alpha_3_code="ECU",
            continent="أمريكا الجنوبية",
            name="الإكوادور",
            capital="كيتو",
        ),
        Country(
            timezones=["أوروبا/تالين"],
            alpha_2_code="EE",
            alpha_3_code="EST",
            continent="أوروبا",
            name="إستونيا",
            capital="تالين",
        ),
        Country(
            timezones=["إفريقيا/القاهرة"],
            alpha_2_code="EG",
            alpha_3_code="EGY",
            continent="إفريقيا",
            name="مصر",
            capital="القاهرة",
        ),
        Country(
            timezones=["إفريقيا/أسمرة"],
            alpha_2_code="ER",
            alpha_3_code="ERI",
            continent="إفريقيا",
            name="إرتيريا",
            capital="أسمرة",
        ),
        Country(
            timezones=["إفريقيا/أديس أبابا"],
            alpha_2_code="ET",
            alpha_3_code="ETH",
            continent="إفريقيا",
            name="إثيوبيا",
            capital="أديس أبابا",
        ),
        Country(
            timezones=["أوروبا/هلسنكي"],
            alpha_2_code="FI",
            alpha_3_code="FIN",
            continent="أوروبا",
            name="فنلندا",
            capital="هلسنكي",
        ),
        Country(
            timezones=["المحيط_الهاديء/فيجي"],
            alpha_2_code="FJ",
            alpha_3_code="FJI",
            continent="أوقيانوسيا",
            name="فيجي",
            capital="سوفا",
        ),
        Country(
            timezones=["أوروبا/باريس"],
            alpha_2_code="FR",
            alpha_3_code="FRA",
            continent="أوروبا",
            name="فرنسا",
            capital="باريس",
        ),
        Country(
            timezones=["إفريقيا/ليبرفيل"],
            alpha_2_code="GA",
            alpha_3_code="GAB",
            continent="إفريقيا",
            name="الغابون",
            capital="ليبرفيل",
        ),
        Country(
            timezones=["آسيا/تبليسي"],
            alpha_2_code="GE",
            alpha_3_code="GEO",
            continent="آسيا",
            name="جورجيا",
            capital="تبليسي",
        ),
        Country(
            timezones=["إفريقيا/أكرا"],
            alpha_2_code="GH",
            alpha_3_code="GHA",
            continent="إفريقيا",
            name="غانا",
            capital="أكرا",
        ),
        Country(
            timezones=["إفريقيا/بانجول"],
            alpha_2_code="GM",
            alpha_3_code="GMB",
            continent="إفريقيا",
            name="غامبيا",
            capital="بانجول",
        ),
        Country(
            timezones=["إفريقيا/كوناكري"],
            alpha_2_code="GN",
            alpha_3_code="GIN",
            continent="إفريقيا",
            name="غينيا",
            capital="كوناكري",
        ),
        Country(
            timezones=["أوروبا/أثينا"],
            alpha_2_code="GR",
            alpha_3_code="GRC",
            continent="أوروبا",
            name="اليونان",
            capital="أثينا",
        ),
        Country(
            timezones=["أمريكا/غواتيمالا"],
            alpha_2_code="GT",
            alpha_3_code="GTM",
            continent="أمريكا الشمالية",
            name="غواتيمالا",
            capital="غواتيمالا سيتي",
        ),
        Country(
            timezones=["أمريكا/غواتيمالا"],
            alpha_2_code="HT",
            alpha_3_code="HTI",
            continent="أمريكا الشمالية",
            name="هايتي",
            capital="بورت أو برانس",
        ),
        Country(
            timezones=["إفريقيا/بيساو"],
            alpha_2_code="GW",
            alpha_3_code="GNB",
            continent="إفريقيا",
            name="غينيا بيساو",
            capital="بيساو",
        ),
        Country(
            timezones=["أمريكا/غيانا"],
            alpha_2_code="GY",
            alpha_3_code="GUY",
            continent="أمريكا الجنوبية",
            name="غيانا",
            capital="جورج تاون",
        ),
        Country(
            timezones=["أمريكا/تيجوسيجالبا"],
            alpha_2_code="HN",
            alpha_3_code="HND",
            continent="أمريكا الشمالية",
            name="هندوراس",
            capital="تيجوسيجالبا",
        ),
        Country(
            timezones=["أوروبا/بودابست"],
            alpha_2_code="HU",
            alpha_3_code="HUN",
            continent="أوروبا",
            name="هنغاريا",
            capital="بودابست",
        ),
        Country(
            timezones=[
                "آسيا/جاكرتا",
                "آسيا/بونتياناك",
                "آسيا/ماكاسار",
                "آسيا/جايابورا",
            ],
            alpha_2_code="ID",
            alpha_3_code="IDN",
            continent="آسيا",
            name="إندونسيا",
            capital="جاكرتا",
        ),
        Country(
            timezones=["أوروبا/دبلن"],
            alpha_2_code="IE",
            alpha_3_code="IRL",
            continent="أوروبا",
            name="إيرلندا",
            capital="دبلن",
        ),
        Country(
            timezones=["آسيا/القدس"],
            alpha_2_code="IL",
            alpha_3_code="ISR",
            continent="آسيا",
            name="فلسطين",
            capital="القدس",
        ),
        Country(
            timezones=["آسيا/كالكتا"],
            alpha_2_code="IN",
            alpha_3_code="IND",
            continent="آسيا",
            name="الهند",
            capital="نيو دلهي",
        ),
        Country(
            timezones=["آسيا/بغداد"],
            alpha_2_code="IQ",
            alpha_3_code="IRQ",
            continent="آسيا",
            name="العراق",
            capital="بغداد",
        ),
        Country(
            timezones=["آسيا/طهران"],
            alpha_2_code="IR",
            alpha_3_code="IRN",
            continent="آسيا",
            name="إيران",
            capital="طهران",
        ),
        Country(
            timezones=["الأطلنطي/ريكيافيك"],
            alpha_2_code="IS",
            alpha_3_code="ISL",
            continent="أوروبا",
            name="آيسلندا",
            capital="ريكيافيك",
        ),
        Country(
            timezones=["أوروبا/روما"],
            alpha_2_code="IT",
            alpha_3_code="ITA",
            continent="أوروبا",
            name="إيطاليا",
            capital="روما",
        ),
        Country(
            timezones=["أمريكا/جامايكا"],
            alpha_2_code="JM",
            alpha_3_code="JAM",
            continent="أمريكا الشمالية",
            name="جامايكا",
            capital="كينغستون",
        ),
        Country(
            timezones=["آسيا/عمّان"],
            alpha_2_code="JO",
            alpha_3_code="JOR",
            continent="آسيا",
            name="الأردن",
            capital="عمّان",
        ),
        Country(
            timezones=["آسيا/طوكيو"],
            alpha_2_code="JP",
            alpha_3_code="JPN",
            continent="آسيا",
            name="اليابان",
            capital="طوكيو",
        ),
        Country(
            timezones=["إفريقيا/نيروبي"],
            alpha_2_code="KE",
            alpha_3_code="KEN",
            continent="إفريقيا",
            name="كينيا",
            capital="نيروبي",
        ),
        Country(
            timezones=["آسيا/بشكيك"],
            alpha_2_code="KG",
            alpha_3_code="KGZ",
            continent="آسيا",
            name="قيرغيزستان",
            capital="بشكيك",
        ),
        Country(
            timezones=[
                "المحيط_الهاديء/تاراوا",
                "المحيط_الهاديء/إيديربيري",
                "المحيط_الهاديء/كريتيماتي",
            ],
            alpha_2_code="KI",
            alpha_3_code="KIR",
            continent="أوقيانوسيا",
            name="كيريباتي",
            capital="جنوب تاراوا",
        ),
        Country(
            timezones=["آسيا/بيونغ_يانغ"],
            alpha_2_code="KP",
            alpha_3_code="PRK",
            continent="آسيا",
            name="كوريا الشمالية",
            capital="بيونغ يانغ",
        ),
        Country(
            timezones=["آسيا/سيؤول"],
            alpha_2_code="KR",
            alpha_3_code="KOR",
            continent="آسيا",
            name="؛كوريا الجنوبية",
            capital="سيؤول",
        ),
        Country(
            timezones=["آسيا/الكويت"],
            alpha_2_code="KW",
            alpha_3_code="KWT",
            continent="آسيا",
            name="الكويت",
            capital="الكويت",
        ),
        Country(
            timezones=["آسيا/بيروت"],
            alpha_2_code="LB",
            alpha_3_code="LBN",
            continent="آسيا",
            name="لبنان",
            capital="بيروت",
        ),
        Country(
            timezones=["أوروبا/فادوز"],
            alpha_2_code="LI",
            alpha_3_code="LIE",
            continent="أوروبا",
            name="ليختنشتاين",
            capital="فادوز",
        ),
        Country(
            timezones=["إفريقيا/مونروفيا"],
            alpha_2_code="LR",
            alpha_3_code="LBR",
            continent="إفريقيا",
            name="ليبيريا",
            capital="مونروفيا",
        ),
        Country(
            timezones=["إفريقيا/ماسيرو"],
            alpha_2_code="LS",
            alpha_3_code="LSO",
            continent="إفريقيا",
            name="ليسوتو",
            capital="ماسيرو",
        ),
        Country(
            timezones=["أوروبا/فيلنيوس"],
            alpha_2_code="LT",
            alpha_3_code="LTU",
            continent="أوروبا",
            name="ليتوانيا",
            capital="فيلنيوس",
        ),
        Country(
            timezones=["أوروبا/لوكسمبرغ"],
            alpha_2_code="LU",
            alpha_3_code="LUX",
            continent="أوروبا",
            name="لوكسمبرغ",
            capital="لوكسمبرغ سيتي",
        ),
        Country(
            timezones=["أوروبا/ربيغ"],
            alpha_2_code="LV",
            alpha_3_code="LVA",
            continent="أوروبا",
            name="لاتفيا",
            capital="ربيغ",
        ),
        Country(
            timezones=["إفريقيا/طرابلس"],
            alpha_2_code="LY",
            alpha_3_code="LBY",
            continent="إفريقيا",
            name="ليبيا",
            capital="طرابلس",
        ),
        Country(
            timezones=["الهندي/أنتاناناريفو"],
            alpha_2_code="MG",
            alpha_3_code="MDG",
            continent="إفريقيا",
            name="مدغشقر",
            capital="أنتاناناريفو",
        ),
        Country(
            timezones=["المحيط_الهاديء/ماجورو", "المحيط_الهاديء/كواجلين_أتول"],
            alpha_2_code="MH",
            alpha_3_code="MHL",
            continent="أوقيانوسيا",
            name="جزر مارشال",
            capital="ماجورو",
        ),
        Country(
            timezones=["أوروبا/سكوبيه"],
            alpha_2_code="MK",
            alpha_3_code="MKD",
            continent="أوروبا",
            name="جمهورية مقدونيا",
            capital="سكوبيه",
        ),
        Country(
            timezones=["إفريقيا/باماكو"],
            alpha_2_code="ML",
            alpha_3_code="MLI",
            continent="إفريقيا",
            name="مالي",
            capital="باماكو",
        ),
        Country(
            timezones=["آسيا/رانغون"],
            alpha_2_code="MM",
            alpha_3_code="MMR",
            continent="آسيا",
            name="ميانمار",
            capital="نايبيداو",
        ),
        Country(
            timezones=["آسيا/أولان_باتور", "آسيا/Hovd", "آسيا/تشويبالسان"],
            alpha_2_code="MN",
            alpha_3_code="MNG",
            continent="آسيا",
            name="مانغوليا",
            capital="أولان باتور",
        ),
        Country(
            timezones=["إفريقيا/نواكشط"],
            alpha_2_code="MR",
            alpha_3_code="MRT",
            continent="إفريقيا",
            name="موريتانيا",
            capital="نواكشط",
        ),
        Country(
            timezones=["أوروبا/مالطا"],
            alpha_2_code="MT",
            alpha_3_code="MLT",
            continent="أوروبا",
            name="مالطا",
            capital="فاليتا",
        ),
        Country(
            timezones=["الهندي/موريشيوس"],
            alpha_2_code="MU",
            alpha_3_code="MUS",
            continent="إفريقيا",
            name="موريشيوس",
            capital="بور لويس",
        ),
        Country(
            timezones=["الهندي/جزر_المالديف"],
            alpha_2_code="MV",
            alpha_3_code="MDV",
            continent="آسيا",
            name="جمهورية المالديف",
            capital="ماليه",
        ),
        Country(
            timezones=["إفريقيا/بلانتاير"],
            alpha_2_code="MW",
            alpha_3_code="MWI",
            continent="إفريقيا",
            name="ملاوي",
            capital="ليلونغوي",
        ),
        Country(
            timezones=[
                "أمريكا/ميكسيكو_سيتي",
                "أمريكا/كانكون",
                "أمريكا/ميرديا",
                "أمريكا/مونتيري",
                "أمريكا/مازاتلان",
                "أمريكا/شيواوا",
                "أمريكا/ارموسييو_سونورا",
                "أمريكا/تيخوانا",
            ],
            alpha_2_code="MX",
            alpha_3_code="MEX",
            continent="أمريكا الشمالية",
            name="المكسيك",
            capital="ميكسيكو سيتي§",
        ),
        Country(
            timezones=["آسيا/كوالا_لامبور", "آسيا/Kuching"],
            alpha_2_code="MY",
            alpha_3_code="MYS",
            continent="آسيا",
            name="ماليزيا",
            capital="كوالا لامبور",
        ),
        Country(
            timezones=["إفريقيا/مابوتو"],
            alpha_2_code="MZ",
            alpha_3_code="MOZ",
            continent="إفريقيا",
            name="موزمبيق",
            capital="مابوتو",
        ),
        Country(
            timezones=["إفريقيا/ويندهوك"],
            alpha_2_code="NA",
            alpha_3_code="NAM",
            continent="إفريقيا",
            name="ناميبيا",
            capital="ويندهوك",
        ),
        Country(
            timezones=["إفريقيا/نيامي"],
            alpha_2_code="NE",
            alpha_3_code="NER",
            continent="إفريقيا",
            name="النيجر",
            capital="نيامي",
        ),
        Country(
            timezones=["إفريقيا/لاغوس"],
            alpha_2_code="NG",
            alpha_3_code="NGA",
            continent="إفريقيا",
            name="نيجيريا",
            capital="أبوجا",
        ),
        Country(
            timezones=["أمريكا/ماناغوا"],
            alpha_2_code="NI",
            alpha_3_code="NIC",
            continent="أمريكا الشمالية",
            name="نيكاراغوا",
            capital="ماناغوا",
        ),
        Country(
            timezones=["أوروبا/أمستردام"],
            alpha_2_code="NL",
            alpha_3_code="NLD",
            continent="أوروبا",
            name="هولندا",
            capital="أمستردام",
        ),
        Country(
            timezones=["أوروبا/أوسلو"],
            alpha_2_code="NO",
            alpha_3_code="NOR",
            continent="أوروبا",
            name="النرويج",
            capital="أوسلو",
        ),
        Country(
            timezones=["آسيا/كاتماندو"],
            alpha_2_code="NP",
            alpha_3_code="NPL",
            continent="آسيا",
            name="النيبال",
            capital="كاتماندو",
        ),
        Country(
            timezones=["المحيط_الهاديء/ناورو"],
            alpha_2_code="NR",
            alpha_3_code="NRU",
            continent="أوقيانوسيا",
            name="ناورو",
            capital="يارين",
        ),
        Country(
            timezones=["المحيط_الهاديء/أوكلاند", "المحيط_الهاديء/تشاتهام"],
            alpha_2_code="NZ",
            alpha_3_code="NZL",
            continent="أوقيانوسيا",
            name="نيوزيلاندا",
            capital="ويلينغتون",
        ),
        Country(
            timezones=["آسيا/مسقط"],
            alpha_2_code="OM",
            alpha_3_code="OMN",
            continent="آسيا",
            name="عمان",
            capital="مسقط",
        ),
        Country(
            timezones=["أمريكا/بنما"],
            alpha_2_code="PA",
            alpha_3_code="PAN",
            continent="أمريكا الشمالية",
            name="بنما",
            capital="بنما",
        ),
        Country(
            timezones=["أمريكا/ليما"],
            alpha_2_code="PE",
            alpha_3_code="PER",
            continent="أمريكا الجنوبية",
            name="البيرو",
            capital="ليما",
        ),
        Country(
            timezones=["المحيط_الهاديء/بورت_مورسبي"],
            alpha_2_code="PG",
            alpha_3_code="PNG",
            continent="أوقيانوسيا",
            name="بابوا غينيا الجديدة",
            capital="بورت مورسبي",
        ),
        Country(
            timezones=["آسيا/مانيلا"],
            alpha_2_code="PH",
            alpha_3_code="PHL",
            continent="آسيا",
            name="الفيليبين",
            capital="مانيلا",
        ),
        Country(
            timezones=["آسيا/كاراتشي"],
            alpha_2_code="PK",
            alpha_3_code="PAK",
            continent="آسيا",
            name="باكستان",
            capital="إسلام أباد",
        ),
        Country(
            timezones=["أوروبا/وارسو"],
            alpha_2_code="PL",
            alpha_3_code="POL",
            continent="أوروبا",
            name="بولندا",
            capital="وارسو",
        ),
        Country(
            timezones=["أوروبا/لشبونة", "الأطلنطي/ماديرا", "الأطلنطي/الأزور"],
            alpha_2_code="PT",
            alpha_3_code="PRT",
            continent="أوروبا",
            name="البرتغال",
            capital="لشبونة",
        ),
        Country(
            timezones=["المحيط_الهاديء/بالاو"],
            alpha_2_code="PW",
            alpha_3_code="PLW",
            continent="أوقيانوسيا",
            name="بالاو",
            capital="نجيرولمد",
        ),
        Country(
            timezones=["أمريكا/أسونسيون"],
            alpha_2_code="PY",
            alpha_3_code="PRY",
            continent="أمريكا الجنوبية",
            name="بابرغوي",
            capital="أسونسيون",
        ),
        Country(
            timezones=["آسيا/قطر"],
            alpha_2_code="QA",
            alpha_3_code="QAT",
            continent="آسيا",
            name="قطر",
            capital="الدوحة",
        ),
        Country(
            timezones=["أوروبا/بوخارست"],
            alpha_2_code="RO",
            alpha_3_code="ROU",
            continent="أوروبا",
            name="رومانيا",
            capital="بوخارست",
        ),
        Country(
            timezones=[
                "أوروبا/كالينينغراد",
                "أوروبا/موسكو",
                "أوروبا/Volgograd",
                "أوروبا/سمارة",
                "آسيا/يكاترينبورغ",
                "آسيا/أومسك",
                "آسيا/نوفوسيبيرسك",
                "آسيا/كراسنوياسك",
                "آسيا/إروتسك",
                "آسيا/ياكوتسك",
                "آسيا/فالديفوستوك",
                "آسيا/ساخالن",
                "آسيا/ماغادان",
                "آسيا/كامشتكا",
                "آسيا/أنادير",
            ],
            alpha_2_code="RU",
            alpha_3_code="RUS",
            continent="أوروبا",
            name="روسيا",
            capital="موسكو",
        ),
        Country(
            timezones=["إفريقيا/كيغالي"],
            alpha_2_code="RW",
            alpha_3_code="RWA",
            continent="إفريقيا",
            name="رواندا",
            capital="كيغالي",
        ),
        Country(
            timezones=["آسيا/الرياض"],
            alpha_2_code="SA",
            alpha_3_code="SAU",
            continent="آسيا",
            name="المملكة العربية السعودية",
            capital="الرياض",
        ),
        Country(
            timezones=["المحيط_الهاديء/غوادالكانال"],
            alpha_2_code="SB",
            alpha_3_code="SLB",
            continent="أوقيانوسيا",
            name="جزر سولمون",
            capital="هونيارا",
        ),
        Country(
            timezones=["الهندي/ماهي"],
            alpha_2_code="SC",
            alpha_3_code="SYC",
            continent="إفريقيا",
            name="سيشل",
            capital="فيكتوريا",
        ),
        Country(
            timezones=["إفريقيا/الخرطوم"],
            alpha_2_code="SD",
            alpha_3_code="SDN",
            continent="إفريقيا",
            name="السودان",
            capital="الخرطوم",
        ),
        Country(
            timezones=["أوروبا/ستوكهولم"],
            alpha_2_code="SE",
            alpha_3_code="SWE",
            continent="أوروبا",
            name="السويد",
            capital="ستوكهولم",
        ),
        Country(
            timezones=["آسيا/سنغافورة"],
            alpha_2_code="SG",
            alpha_3_code="SGP",
            continent="آسيا",
            name="سنغافورة",
            capital="سنغافورة",
        ),
        Country(
            timezones=["أوروبا/ليوبليانا"],
            alpha_2_code="SI",
            alpha_3_code="SVN",
            continent="أوروبا",
            name="سلوفانيا",
            capital="ليوبليانا",
        ),
        Country(
            timezones=["أوروبا/براتيسلافا"],
            alpha_2_code="SK",
            alpha_3_code="SVK",
            continent="أوروبا",
            name="سلوفاكيا",
            capital="براتيسلافا",
        ),
        Country(
            timezones=["إفريقيا/فريتاون"],
            alpha_2_code="SL",
            alpha_3_code="SLE",
            continent="إفريقيا",
            name="سيراليون",
            capital="فريتاون",
        ),
        Country(
            timezones=["أوروبا/سان_مارينو"],
            alpha_2_code="SM",
            alpha_3_code="SMR",
            continent="أوروبا",
            name="جمهورية سان مارينو",
            capital="سان مارينو",
        ),
        Country(
            timezones=["إفريقيا/داكار"],
            alpha_2_code="SN",
            alpha_3_code="SEN",
            continent="إفريقيا",
            name="السنغال",
            capital="داكار",
        ),
        Country(
            timezones=["إفريقيا/مقديشو"],
            alpha_2_code="SO",
            alpha_3_code="SOM",
            continent="إفريقيا",
            name="الصومال",
            capital="مقديشو",
        ),
        Country(
            timezones=["أمريكا/باراماريبو"],
            alpha_2_code="SR",
            alpha_3_code="SUR",
            continent="أمريكا الجنوبية",
            name="Suriname",
            capital="باراماريبو",
        ),
        Country(
            timezones=["إفريقيا/ساو_تومي"],
            alpha_2_code="ST",
            alpha_3_code="STP",
            continent="إفريقيا",
            name=" ساو تومي وبرينسيب",
            capital="ساو تومي",
        ),
        Country(
            timezones=["آسيا/دممشق"],
            alpha_2_code="SY",
            alpha_3_code="SYR",
            continent="آسيا",
            name="سوريا",
            capital="دمشق",
        ),
        Country(
            timezones=["إفريقيا/لومي"],
            alpha_2_code="TG",
            alpha_3_code="TGO",
            continent="إفريقيا",
            name="توغو",
            capital="لومي",
        ),
        Country(
            timezones=["آسيا/بانغوك"],
            alpha_2_code="TH",
            alpha_3_code="THA",
            continent="آسيا",
            name="تايلند",
            capital="بناغوك",
        ),
        Country(
            timezones=["آسيا/دوشنبه"],
            alpha_2_code="TJ",
            alpha_3_code="TJK",
            continent="آسيا",
            name="طاجكيستان",
            capital="دوشنبه",
        ),
        Country(
            timezones=["آسيا/عشق_آباد"],
            alpha_2_code="TM",
            alpha_3_code="TKM",
            continent="آسيا",
            name="تركمانستان",
            capital="عشق آباد",
        ),
        Country(
            timezones=["إفريقيا/تونس"],
            alpha_2_code="TN",
            alpha_3_code="TUN",
            continent="إفريقيا",
            name="تونس",
            capital="تونس",
        ),
        Country(
            timezones=["المحيط_الهاديء/تونغاتابو"],
            alpha_2_code="TO",
            alpha_3_code="TON",
            continent="أوقيانوسيا",
            name="تونغا",
            capital="نوكو ألوفا",
        ),
        Country(
            timezones=["أوروبا/إسطنبول"],
            alpha_2_code="TR",
            alpha_3_code="TUR",
            continent="آسيا",
            name="تركيا",
            capital="أنقرة",
        ),
        Country(
            timezones=["أمريكا/بورت_أوف_سبين"],
            alpha_2_code="TT",
            alpha_3_code="TTO",
            continent="أمريكا الشمالية",
            name="ترينيداد وتوباغو",
            capital="بورت أوف سبين",
        ),
        Country(
            timezones=["المحيط_الهاديء/فونافوتي"],
            alpha_2_code="TV",
            alpha_3_code="TUV",
            continent="أوقيانوسيا",
            name="توفالو",
            capital="فونافوتي",
        ),
        Country(
            timezones=["إفريقيا/دار_السلام"],
            alpha_2_code="TZ",
            alpha_3_code="TZA",
            continent="إفريقيا",
            name="تانزانيا",
            capital="دودوما",
        ),
        Country(
            timezones=[
                "أوروبا/كييف",
                "أوروبا/أوجهورود",
                "أوروبا/زاباروجيا",
                "أوروبا/سيمفروبول",
            ],
            alpha_2_code="UA",
            alpha_3_code="UKR",
            continent="أوروبا",
            name="أوكرانيا",
            capital="كييف",
        ),
        Country(
            timezones=["إفريقيا/كامبالا"],
            alpha_2_code="UG",
            alpha_3_code="UGA",
            continent="إفريقيا",
            name="أوغندا",
            capital="كامبالا",
        ),
        Country(
            timezones=[
                "أمريكا/نيويورك",
                "أمريكا/ديترويت",
                "أمريكا/كنتاكي/لويسفيل",
                "أمريكا/كنتاكي/مونتيسللو",
                "أمريكا/إنديانا/إنديانابولس",
                "أمريكا/إنديانا/مارنغو",
                "أمريكا/إنديانا/نوكس",
                "أمريكا/إنديانا/فيفاي",
                "أمريكا/شيكاغو",
                "أمريكا/إنديانا/فانسان",
                "أمريكا/إنديانا/بيترزبيرغ",
                "أمريكا/مينومني",
                "أمريكا/نورث_داكوتا/سينتر",
                "أمريكا/نورث_داكوتا/نيو_سالم",
                "أمريكا/دنفر",
                "أمريكا/بويسي",
                "أمريكا/شيبروك",
                "أمريكا/فينيكس",
                "أمريكا/لوس_أنجيلوس",
                "أمريكا/أنكوريج",
                "أمريكا/جونو",
                "أمريكا/ياكوتات",
                "أمريكا/نوم",
                "أمريكا/أداك",
                "المحيط_الهاديء/هونولولو",
            ],
            alpha_2_code="US",
            alpha_3_code="USA",
            continent="أمريكا الشمالية",
            name="الولايات المتحدة الأمريكية",
            capital="واشنطن",
        ),
        Country(
            timezones=["أمريكا/مونتفيدو"],
            alpha_2_code="UY",
            alpha_3_code="URY",
            continent="أمريكا الجنوبية",
            name="أوروغواي",
            capital="مونتفيدو",
        ),
        Country(
            timezones=["آسيا/سمرقند", "آسيا/طشقند"],
            alpha_2_code="UZ",
            alpha_3_code="UZB",
            continent="آسيا",
            name="أوزبكستان",
            capital="طشقند",
        ),
        Country(
            timezones=["أوروبا/الفاتيكان"],
            alpha_2_code="VA",
            alpha_3_code="VAT",
            continent="أوروبا",
            name="الفاتيكان",
            capital="الفاتيكان",
        ),
        Country(
            timezones=["أمريكا/كاركاس"],
            alpha_2_code="VE",
            alpha_3_code="VEN",
            continent="أمريكا الجنوبية",
            name="فنزويلا",
            capital="كاركاس",
        ),
        Country(
            timezones=["آسيا/سايغون"],
            alpha_2_code="VN",
            alpha_3_code="VNM",
            continent="آسيا",
            name="فيتنام",
            capital="هانوي",
        ),
        Country(
            timezones=["المحيط_الهاديء/أيفاتي"],
            alpha_2_code="VU",
            alpha_3_code="VUT",
            continent="أوقيانوسيا",
            name="فانواتو",
            capital="بورت فيلا",
        ),
        Country(
            timezones=["آسيا/عدن"],
            alpha_2_code="YE",
            alpha_3_code="YEM",
            continent="آسيا",
            name="اليمن",
            capital="صنعاء",
        ),
        Country(
            timezones=["إفريقيا/لوساكا"],
            alpha_2_code="ZM",
            alpha_3_code="ZMB",
            continent="إفريقيا",
            name="زامبيا",
            capital="لوساكا",
        ),
        Country(
            timezones=["إفريقيا/هراري"],
            alpha_2_code="ZW",
            alpha_3_code="ZWE",
            continent="إفريقيا",
            name="زيمبابوي",
            capital="هراري",
        ),
        Country(
            timezones=["إفريقيا/الجزائر"],
            alpha_2_code="DZ",
            alpha_3_code="DZA",
            continent="إفريقيا",
            name="الجزائر",
            capital="الجزائر",
        ),
        Country(
            timezones=["أوروبا/سراييفو"],
            alpha_2_code="BA",
            alpha_3_code="BIH",
            continent="أوروبا",
            name="البوسنة والهرسك",
            capital="سراييفو",
        ),
        Country(
            timezones=["آسيا/بنوم_بنه"],
            alpha_2_code="KH",
            alpha_3_code="KHM",
            continent="آسيا",
            name="كمبوديا",
            capital="بنوم بنه",
        ),
        Country(
            timezones=["إفريقيا/بانغي"],
            alpha_2_code="CF",
            alpha_3_code="CAF",
            continent="إفريقيا",
            name="جمهورية أفريقيا الوسطى",
            capital="بانغي",
        ),
        Country(
            timezones=["إفريقيا/نجامينا"],
            alpha_2_code="TD",
            alpha_3_code="TCD",
            continent="إفريقيا",
            name="تشاد",
            capital="نجامينا",
        ),
        Country(
            timezones=["الهندي/كومورو"],
            alpha_2_code="KM",
            alpha_3_code="COM",
            continent="إفريقيا",
            name="جزر القمر",
            capital="موروني",
        ),
        Country(
            timezones=["أوروبا/زغرب"],
            alpha_2_code="HR",
            alpha_3_code="HRV",
            continent="أوروبا",
            name="كرواتيا",
            capital="زغرب",
        ),
        Country(
            timezones=["آسيا/ديلي"],
            alpha_2_code="TL",
            alpha_3_code="TLS",
            continent="آسيا",
            name="تيمور الشرقية",
            capital="ديلي",
        ),
        Country(
            timezones=["أمريكا/السلفادور"],
            alpha_2_code="SV",
            alpha_3_code="SLV",
            continent="أمريكا الشمالية",
            name="السلفادور",
            capital="سان سلفادور",
        ),
        Country(
            timezones=["إفريقيا/مالابو"],
            alpha_2_code="GQ",
            alpha_3_code="GNQ",
            continent="إفريقيا",
            name="غينيا الاستوائية",
            capital="مالابو",
        ),
        Country(
            timezones=["أمريكا/غرينادا"],
            alpha_2_code="GD",
            alpha_3_code="GRD",
            continent="أمريكا الشمالية",
            name="غرينادا",
            capital="سانت جورجز",
        ),
        Country(
            timezones=[
                "آسيا/ألماتي",
                "آسيا/كيزيلوردا",
                "آسيا/أقتوبي",
                "آسيا/أقتاو",
                "آسيا/أورال",
            ],
            alpha_2_code="KZ",
            alpha_3_code="KAZ",
            continent="آسيا",
            name="كازاخستان",
            capital="أستانة",
        ),
        Country(
            timezones=["آسيا/فيينتيان"],
            alpha_2_code="LA",
            alpha_3_code="LAO",
            continent="آسيا",
            name="لاوس",
            capital="فيينتيان",
        ),
        Country(
            timezones=[
                "المحيط_الهاديء/تشوك",
                "المحيط_الهاديء/بونابي",
                "المحيط_الهاديء/كورساي",
            ],
            alpha_2_code="FM",
            alpha_3_code="FSM",
            continent="أوقيانوسيا",
            name="ولايات ميكرونيسيا المتحدة",
            capital="باليكير",
        ),
        Country(
            timezones=["أوروبا/كيشيناو"],
            alpha_2_code="MD",
            alpha_3_code="MDA",
            continent="أوروبا",
            name="مولدافيا",
            capital="كيشيناو",
        ),
        Country(
            timezones=["أوروبا/موناكو"],
            alpha_2_code="MC",
            alpha_3_code="MCO",
            continent="أوروبا",
            name="موناكو",
            capital="موناكو",
        ),
        Country(
            timezones=["أوروبا/بودغوريتسا"],
            alpha_2_code="ME",
            alpha_3_code="MNE",
            continent="أوروبا",
            name="الجبل الأسود",
            capital="بودغوريتسا",
        ),
        Country(
            timezones=["إفريقيا/الدار_البيضاء"],
            alpha_2_code="MA",
            alpha_3_code="MAR",
            continent="إفريقيا",
            name="المغرب",
            capital="الرباط",
        ),
        Country(
            timezones=["أمريكا/سانت_كيتس"],
            alpha_2_code="KN",
            alpha_3_code="KNA",
            continent="أمريكا الشمالية",
            name="سانت كيتس ونيفيس",
            capital="باستير",
        ),
        Country(
            timezones=["أمريكا/سانت_لوسيا"],
            alpha_2_code="LC",
            alpha_3_code="LCA",
            continent="أمريكا الشمالية",
            name="سانت لوسيا",
            capital="كاستريس",
        ),
        Country(
            timezones=["أمريكا/سينت_فينسينت"],
            alpha_2_code="VC",
            alpha_3_code="VCT",
            continent="أمريكا الشمالية",
            name="سانت فينسنت والغرينادين",
            capital="كينغستاون",
        ),
        Country(
            timezones=["المحيط_الهاديء/أبيا"],
            alpha_2_code="WS",
            alpha_3_code="WSM",
            continent="أوقيانوسيا",
            name="ساموا",
            capital="أبيا",
        ),
        Country(
            timezones=["أوروبا/بلغراد"],
            alpha_2_code="RS",
            alpha_3_code="SRB",
            continent="أوروبا",
            name="صربيا",
            capital="بلغراد",
        ),
        Country(
            timezones=["إفريقيا/جوهانسبرغ"],
            alpha_2_code="ZA",
            alpha_3_code="ZAF",
            continent="إفريقيا",
            name="جنوب إفريقيا",
            capital="بريتوريا",
        ),
        Country(
            timezones=["أوروبا/مدريد", "إفريقيا/سبتة", "الأطلنطي/الكناري"],
            alpha_2_code="ES",
            alpha_3_code="ESP",
            continent="أوروبا",
            name="إسبانيا",
            capital="مدريد",
        ),
        Country(
            timezones=["آسيا/كولمبو"],
            alpha_2_code="LK",
            alpha_3_code="LKA",
            continent="آسيا",
            name="سريلانكا",
            capital="سري جاياواردنابورا كوتي",
        ),
        Country(
            timezones=["إفريقيا/مبابان"],
            alpha_2_code="SZ",
            alpha_3_code="SWZ",
            continent="إفريقيا",
            name="سوازيلاند",
            capital="مبابان",
        ),
        Country(
            timezones=["أوروبا/زيورخ"],
            alpha_2_code="CH",
            alpha_3_code="CHE",
            continent="أوروبا",
            name="سويسرا",
            capital="برن",
        ),
        Country(
            timezones=["آسيا/دبي"],
            alpha_2_code="AE",
            alpha_3_code="ARE",
            continent="آسيا",
            name="الإمارات العربية المتحدة",
            capital="أبو ظبي",
        ),
        Country(
            timezones=["أوروبا/لندن"],
            alpha_2_code="GB",
            alpha_3_code="GBR",
            continent="أوروبا",
            name="المملكة المتحدة",
            capital="لندن",
        ),
    ]

    AM_PM = {
        "AM": "ص",
        "PM": "م",
    }

    def month_name(self) -> str:
        month = self.date("%m")
        return self.MONTH_NAMES[month]

    def am_pm(self) -> str:
        date = self.date("%p")
        return self.AM_PM[date]

    def day_of_week(self) -> str:
        day = self.date("%w")
        return self.DAY_NAMES[day]
