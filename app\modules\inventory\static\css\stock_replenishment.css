/* Styles spécifiques pour le système d'approvisionnement de stock */

/* Variables CSS pour la cohérence des couleurs */
:root {
    --replenishment-primary: #28a745;
    --replenishment-secondary: #6c757d;
    --replenishment-success: #20c997;
    --replenishment-warning: #ffc107;
    --replenishment-danger: #dc3545;
    --replenishment-info: #17a2b8;
    --replenishment-bg: #f8f9fa;
    --replenishment-border: #dee2e6;
    --replenishment-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Container principal avec background différencié */
.stock-replenishment-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding-bottom: 2rem;
}

/* En-tête de l'approvisionnement */
.replenishment-header {
    background: linear-gradient(135deg, var(--replenishment-primary) 0%, var(--replenishment-success) 100%);
    color: white;
    box-shadow: var(--replenishment-shadow);
    margin-bottom: 1rem;
}

.replenishment-header h2 {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-actions .btn {
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Cartes de statistiques */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--replenishment-shadow);
    border: none;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: currentColor;
}

.stat-card.bg-warning::before { background: var(--replenishment-warning); }
.stat-card.bg-danger::before { background: var(--replenishment-danger); }
.stat-card.bg-info::before { background: var(--replenishment-info); }
.stat-card.bg-success::before { background: var(--replenishment-success); }

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.stat-card .stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.stat-card .stat-content p {
    color: var(--replenishment-secondary);
    margin-bottom: 0;
    font-weight: 500;
}

/* Carte de sélection de mode */
.mode-selector-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
}

.mode-selector-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 2rem;
}

.mode-selector-card .card-header h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.mode-selector-card .card-body {
    padding: 2rem;
}

/* Options de mode */
.mode-option {
    background: #f8f9fa;
    border: 2px solid var(--replenishment-border);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.mode-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.mode-option:hover::before {
    left: 100%;
}

.mode-option:hover {
    border-color: var(--replenishment-primary);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.15);
}

.mode-option.selected {
    border-color: var(--replenishment-primary);
    background: rgba(40, 167, 69, 0.05);
}

.mode-option .mode-icon {
    font-size: 3rem;
    color: var(--replenishment-primary);
    margin-bottom: 1rem;
}

.mode-option h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.mode-option p {
    color: var(--replenishment-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.mode-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.mode-features li {
    padding: 0.5rem 0;
    color: #495057;
}

.mode-features i {
    margin-right: 0.5rem;
    width: 16px;
}

.mode-action .btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.mode-action .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Cartes d'actions rapides */
.quick-action-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--replenishment-shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--replenishment-border);
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.quick-action-card h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

.quick-action-card h6 i {
    color: var(--replenishment-primary);
    margin-right: 0.5rem;
}

/* Mode POS spécifique - Design moderne */
.pos-replenishment-container {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 50%, #e1f5fe 100%);
    min-height: 100vh;
    position: relative;
    padding-top: 1rem; /* Marge équilibrée entre navbar et en-tête */
}

.pos-replenishment-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(40, 167, 69, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(32, 201, 151, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.pos-replenishment-header {
    background: linear-gradient(135deg, var(--replenishment-success) 0%, var(--replenishment-primary) 100%);
    color: white;
    padding: 0.5rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 10;
    margin-bottom: 1rem; /* Marge en bas de l'en-tête */
}

.pos-replenishment-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 25%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.3) 75%,
        transparent 100%);
}

/* Barre de recherche dans l'en-tête */
.search-section-header {
    width: 300px;
}

.search-section-header .input-group {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section-header .input-group-text {
    border-radius: 20px 0 0 20px;
}

.search-section-header .form-control {
    border-left: none;
}

.search-section-header .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

/* Espacement équilibré pour le contenu */
.pos-content-reduced-margin {
    margin-top: 0; /* Suppression de la marge négative pour un espacement équilibré */
}

/* Boutons de catégories dynamiques */
.category-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-btn {
    background: white;
    border: 2px solid var(--replenishment-border);
    border-radius: 6px;
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #2c3e50;
    flex: 0 0 auto;
    min-width: 80px;
    max-width: 120px;
    text-align: center;
}

.category-btn:hover {
    background: var(--replenishment-primary);
    color: white;
    border-color: var(--replenishment-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.category-btn.active {
    background: var(--replenishment-primary);
    color: white;
    border-color: var(--replenishment-primary);
}

/* Styles pour la section fournisseur compacte */
.supplier-section .card {
    border: 1px solid var(--replenishment-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.supplier-section .form-label {
    margin-bottom: 0.25rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Styles pour la modale fournisseur améliorée */
.supplier-modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.supplier-modal-header {
    background: linear-gradient(135deg, var(--replenishment-success) 0%, var(--replenishment-primary) 100%);
    color: white;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
}

.supplier-modal-header .modal-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.supplier-modal-body {
    padding: 1.5rem;
    background: #f8f9fa;
}

.supplier-selection-container {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.supplier-field-group {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.supplier-field-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.supplier-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.supplier-select:focus {
    border-color: var(--replenishment-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.supplier-modal-footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    gap: 0.75rem;
}

.supplier-modal-footer .btn {
    border-radius: 8px;
    padding: 0.5rem 1.25rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.supplier-modal-footer .btn-success {
    background: var(--replenishment-primary);
    border-color: var(--replenishment-primary);
}

.supplier-modal-footer .btn-success:hover {
    background: var(--replenishment-success);
    border-color: var(--replenishment-success);
    transform: translateY(-1px);
}

/* Grille d'articles pour le mode POS - 5 par ligne */
.items-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    padding: 8px;
    min-height: auto; /* Hauteur adaptative au contenu */
    max-height: 75vh;
    overflow-y: auto;
    grid-auto-rows: min-content; /* Les lignes s'adaptent au contenu */
}

.item-card {
    background: white;
    border-radius: 8px;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(40, 167, 69, 0.1), transparent);
    transition: left 0.5s;
}

.item-card:hover::before {
    left: 100%;
}

.item-card:hover {
    border-color: var(--replenishment-primary);
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
}

.item-card img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    margin: 0 auto 0.25rem auto;
    border: 1px solid #f8f9fa;
    transition: all 0.3s ease;
    flex-shrink: 0;
    display: block;
}

.item-card:hover img {
    border-color: var(--replenishment-primary);
    transform: scale(1.05);
}

.item-card .item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 0.7rem;
    line-height: 1.1;
    height: 2rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.item-card .item-price {
    color: var(--replenishment-primary);
    font-weight: 700;
    margin-bottom: 0.125rem;
    font-size: 0.75rem;
}

.item-card .item-stock {
    color: var(--replenishment-secondary);
    font-size: 0.65rem;
    background: #f8f9fa;
    padding: 0.1rem 0.3rem;
    border-radius: 8px;
    display: inline-block;
}

/* Ticket d'achat - Optimisé pour la colonne droite avec scroll */
.purchase-ticket {
    background: white;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    border: 1px solid var(--replenishment-border);
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
}

.ticket-header {
    background: linear-gradient(135deg, var(--replenishment-primary) 0%, var(--replenishment-success) 100%);
    color: white;
    padding: 1.25rem;
    border-radius: 15px 15px 0 0;
    position: relative;
}

.ticket-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.ticket-header {
    flex-shrink: 0; /* Empêche la compression du header */
}

.ticket-items {
    padding: 1rem;
    flex: 1;
    overflow-y: auto;
    background: #fafbfc;
    min-height: 0; /* Important pour permettre le flex shrink */
}

.ticket-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--replenishment-border);
    transition: all 0.3s ease;
}

.ticket-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateX(3px);
}

.ticket-total {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.25rem;
    border-radius: 0 0 15px 15px;
    border-top: 3px solid var(--replenishment-primary);
    flex-shrink: 0; /* Empêche la compression de cette section */
}

/* Colonne droite scrollable */
.col-lg-3.slide-in-right,
.col-lg-4 {
    height: 100vh;
    overflow-y: auto;
    padding-right: 15px;
    padding-left: 15px;
}

/* Numpad pour le mode POS - Optimisé pour la colonne gauche */
.numpad-container {
    background: white;
    border-radius: 15px;
    padding: 1.25rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--replenishment-border);
    position: sticky;
    top: 1rem;
}

.numpad-display {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 0.75rem;
    text-align: right;
    font-size: 1.4rem;
    font-weight: 700;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

.numpad-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
}

.numpad-btn {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid var(--replenishment-border);
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.numpad-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(40, 167, 69, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.numpad-btn:hover::before {
    width: 100%;
    height: 100%;
}

.numpad-btn:hover {
    background: linear-gradient(135deg, var(--replenishment-primary) 0%, var(--replenishment-success) 100%);
    color: white;
    border-color: var(--replenishment-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.numpad-btn.special {
    background: linear-gradient(135deg, var(--replenishment-secondary) 0%, #495057 100%);
    color: white;
}

.numpad-btn.special:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(73, 80, 87, 0.3);
}

/* Responsive design pour la nouvelle mise en page */
@media (max-width: 1200px) {
    .items-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .search-section-header {
        width: 250px;
    }
}

@media (max-width: 992px) {
    .items-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
    }

    .numpad-btn {
        padding: 0.6rem;
        font-size: 1rem;
    }

    .numpad-display {
        font-size: 1.2rem;
        min-height: 45px;
    }

    .search-section-header {
        width: 200px;
    }

    .category-btn {
        min-width: 70px;
        max-width: 100px;
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }
}

@media (max-width: 768px) {
    .mode-selector-card .card-body {
        padding: 1rem;
    }

    .mode-option {
        margin-bottom: 1rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .items-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 4px;
        min-height: auto; /* Hauteur adaptative au contenu */
        max-height: 60vh;
        grid-auto-rows: min-content; /* Les lignes s'adaptent au contenu */
    }

    .item-card {
        padding: 0.4rem;
        height: 100px;
    }

    .item-card img {
        width: 30px;
        height: 30px;
    }

    .item-card .item-name {
        font-size: 0.65rem;
        height: 1.8rem;
    }

    .item-card .item-price {
        font-size: 0.7rem;
    }

    .item-card .item-stock {
        font-size: 0.6rem;
    }

    .numpad-btn {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .numpad-display {
        font-size: 1.1rem;
        min-height: 40px;
        padding: 0.5rem;
    }

    .numpad-container {
        padding: 0.75rem;
    }

    .purchase-ticket {
        height: auto;
        max-height: 70vh;
        position: relative;
    }

    .ticket-items {
        max-height: 30vh;
    }

    /* Colonne droite responsive */
    .col-lg-3.slide-in-right,
    .col-lg-4 {
        height: auto;
        overflow-y: visible;
        margin-top: 1rem;
    }

    .search-section-header {
        display: none;
    }

    .pos-replenishment-header h4 {
        font-size: 1.1rem;
    }

    .category-btn {
        min-width: 60px;
        max-width: 90px;
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
    }

    .category-buttons-container {
        gap: 0.25rem;
    }

    .supplier-modal-body {
        padding: 1rem;
    }

    .supplier-field-group {
        padding: 0.75rem;
    }

    .supplier-section .card-body {
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .items-grid {
        grid-template-columns: 1fr;
        padding: 0.5rem;
    }

    .numpad-grid {
        gap: 0.5rem;
    }

    .numpad-btn {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    /* Colonne droite sur mobile */
    .col-lg-3.slide-in-right,
    .col-lg-4 {
        height: auto;
        overflow-y: visible;
        padding: 0.5rem;
    }

    .purchase-ticket {
        height: auto;
        max-height: none;
        margin-bottom: 1rem;
    }

    .ticket-items {
        max-height: 40vh;
    }
}

/* Animations améliorées */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Effet shimmer pour les cartes en chargement */
.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Améliorations pour les boutons de catégories */
.category-buttons .btn {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.category-buttons .btn:hover::before {
    left: 100%;
}

.category-buttons .btn.active {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-buttons .btn-outline-primary.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

.category-buttons .btn-outline-success.active {
    background: linear-gradient(135deg, var(--replenishment-success) 0%, var(--replenishment-primary) 100%);
    border-color: var(--replenishment-success);
}

.category-buttons .btn-outline-info.active {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
}

/* États de chargement */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--replenishment-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Amélioration de la barre de recherche */
.search-section .input-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section .input-group-text {
    background: linear-gradient(135deg, var(--replenishment-primary) 0%, var(--replenishment-success) 100%);
    color: white;
    border: none;
}

.search-section .form-control {
    border: none;
    padding: 0.75rem 1rem;
    font-size: 1rem;
}

.search-section .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

/* Styles pour le tableau des commandes */
.table-container {
    border-radius: 15px;
    overflow-x: auto;
    overflow-y: visible;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    max-width: 100%;
    position: relative;
}

.table-container::-webkit-scrollbar {
    height: 12px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

.table-container::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 6px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

.orders-table {
    margin-bottom: 0;
    font-size: 0.85rem;
    min-width: 1800px; /* Force une largeur minimale pour le scroll */
    white-space: nowrap;
}

.orders-table thead th {
    background: linear-gradient(45deg, #343a40, #495057);
    color: white;
    border: none;
    padding: 1rem 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.orders-table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.orders-table tbody tr:hover {
    background-color: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.orders-table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border: none;
    white-space: nowrap;
}

/* Colonnes sticky */
.sticky-col {
    position: sticky;
    left: 0;
    background: white;
    z-index: 5;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
}

.sticky-actions {
    position: sticky;
    right: 0;
    background: white;
    z-index: 5;
    box-shadow: -2px 0 4px rgba(0,0,0,0.1);
    min-width: 120px;
}

/* Styles pour améliorer la lisibilité du texte */
.date-text {
    color: #2c3e50 !important;
    font-weight: 600;
    font-size: 0.85rem;
}

.time-text {
    color: #6c757d !important;
    font-size: 0.75rem;
}

.no-data {
    color: #adb5bd !important;
    font-style: italic;
    font-size: 0.8rem;
}

.notes-text {
    color: #495057 !important;
    font-size: 0.8rem;
    font-weight: 500;
}

.discount-value {
    color: #e67e22 !important;
    font-weight: 700;
    font-size: 0.85rem;
}

.discount-type {
    color: #95a5a6 !important;
    font-size: 0.7rem;
}

.tax-rate {
    color: #3498db !important;
    font-weight: 600;
    font-size: 0.85rem;
}

.tax-amount {
    color: #7f8c8d !important;
    font-size: 0.75rem;
}

.total-amount {
    color: #27ae60 !important;
    font-size: 0.9rem;
}

.subtotal-amount {
    color: #95a5a6 !important;
    font-size: 0.75rem;
}

/* Styles pour les badges dans le tableau */
.supplier-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.items-count {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-weight: 600;
}

/* Styles pour la liste des produits */
.products-cell {
    max-width: 200px;
    min-width: 150px;
}

.products-list {
    max-height: 100px;
    overflow-y: auto;
    padding: 0.25rem;
}

.products-list::-webkit-scrollbar {
    width: 4px;
}

.products-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.products-list::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 2px;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.25rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

.product-name {
    color: #2c3e50 !important;
    font-weight: 600;
    font-size: 0.75rem;
    flex: 1;
    margin-right: 0.5rem;
}

.product-qty {
    color: #7f8c8d !important;
    font-size: 0.7rem;
    font-weight: 500;
    white-space: nowrap;
}

/* Styles pour les boutons d'action dans le tableau */
.actions-group .btn {
    border-radius: 6px;
    font-size: 0.7rem;
    padding: 0.35rem 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-bottom: 2px;
    white-space: nowrap;
    min-width: 80px;
    text-align: left;
}

.actions-group .btn:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.actions-group .btn:last-child {
    margin-bottom: 0;
}

.actions-group .btn i {
    margin-right: 0.25rem;
    width: 12px;
    text-align: center;
}

/* Cellules spécifiques */
.amount-cell {
    text-align: right;
    min-width: 100px;
}

.date-cell {
    min-width: 90px;
}

.notes-cell {
    max-width: 150px;
    min-width: 100px;
}

.discount-cell {
    min-width: 80px;
}

.tax-cell {
    min-width: 70px;
}

/* Amélioration de la pagination */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #667eea;
    transition: all 0.2s ease;
    font-weight: 500;
}

.pagination .page-link:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

/* Responsive pour le tableau */
@media (max-width: 1400px) {
    .table {
        font-size: 0.8rem;
    }

    .table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }

    .table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .btn-group-vertical .btn {
        font-size: 0.7rem;
        padding: 0.3rem 0.5rem;
    }
}

@media (max-width: 1200px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .table thead th {
        padding: 0.5rem 0.4rem;
    }

    .table tbody td {
        padding: 0.5rem 0.4rem;
    }
}

@media (max-width: 768px) {
    .table-responsive {
        border-radius: 10px;
    }

    .table {
        font-size: 0.7rem;
    }

    .table thead th {
        padding: 0.5rem 0.3rem;
        font-size: 0.65rem;
    }

    .table tbody td {
        padding: 0.5rem 0.3rem;
    }

    .btn-group-vertical .btn {
        font-size: 0.65rem;
        padding: 0.25rem 0.4rem;
    }

    .badge {
        font-size: 0.65rem !important;
        padding: 0.3rem 0.6rem !important;
    }
}

/* ===== STYLES POUR LA RÉCEPTION PARTIELLE ===== */

/* Styles pour les quantités dans la modal de réception partielle */
.receive-quantity {
    font-weight: bold;
    text-align: center;
    transition: all 0.2s ease-in-out;
}

.receive-quantity:focus {
    border-color: var(--replenishment-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    transform: scale(1.02);
}

.receive-quantity:hover {
    border-color: var(--replenishment-success);
}

/* Badges pour les quantités */
.quantity-badge {
    font-size: 0.9em;
    padding: 0.5em 0.8em;
    border-radius: 0.5rem;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.quantity-badge.bg-primary {
    background-color: var(--replenishment-primary) !important;
}

.quantity-badge.bg-success {
    background-color: var(--replenishment-success) !important;
}

.quantity-badge.bg-warning {
    background-color: var(--replenishment-warning) !important;
    color: #000 !important;
}

/* Informations de quantité */
.quantity-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--replenishment-border);
    box-shadow: var(--replenishment-shadow);
}

.quantity-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.quantity-item {
    text-align: center;
    flex: 1;
    min-width: 120px;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.7);
    transition: all 0.2s ease-in-out;
}

.quantity-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quantity-item .number {
    font-size: 1.8rem;
    font-weight: bold;
    display: block;
    margin-bottom: 0.25rem;
}

.quantity-item .label {
    font-size: 0.85rem;
    color: var(--replenishment-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Styles pour les badges de statut améliorés */
.status-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
    transition: all 0.2s ease-in-out;
}

.status-badge:hover {
    transform: scale(1.05);
}

.status-pending {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-color: #ffeaa7;
}

.status-partial {
    background: linear-gradient(135deg, #cff4fc 0%, #9eeaf9 100%);
    color: #055160;
    border-color: #9eeaf9;
}

.status-received {
    background: linear-gradient(135deg, #d1e7dd 0%, #a3cfbb 100%);
    color: #0a3622;
    border-color: #a3cfbb;
}

/* Animation pour les inputs de quantité */
.receive-quantity {
    transition: all 0.2s ease-in-out;
}

.receive-quantity:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Styles pour les notifications toast améliorées */
.notification-toast {
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Styles pour les boutons d'action rapide */
.quick-action-btn {
    transition: all 0.2s ease-in-out;
    border-radius: 0.5rem;
    font-weight: 500;
}

.quick-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Amélioration de la modal de réception partielle */
.modal-header.bg-primary {
    background: linear-gradient(135deg, var(--replenishment-primary) 0%, var(--replenishment-success) 100%) !important;
    border-bottom: none;
}

.modal-xl {
    max-width: 1200px;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Table responsive améliorée pour la modal */
.modal .table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--replenishment-shadow);
}

.modal .table {
    margin-bottom: 0;
}

.modal .table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--replenishment-border);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    color: var(--replenishment-secondary);
}

.modal .table tbody tr {
    transition: all 0.2s ease-in-out;
}

.modal .table tbody tr:hover {
    background-color: rgba(40, 167, 69, 0.05);
    transform: scale(1.01);
}

/* Styles pour les alertes d'information */
.alert-info {
    background: linear-gradient(135deg, #cff4fc 0%, #9eeaf9 100%);
    border: 1px solid #9eeaf9;
    border-radius: 0.75rem;
    color: #055160;
}

.alert-info .fas {
    color: var(--replenishment-info);
}

/* Responsive pour les quantités */
@media (max-width: 768px) {
    .quantity-summary {
        flex-direction: column;
        gap: 1rem;
    }

    .quantity-item {
        min-width: 100%;
    }

    .quantity-item .number {
        font-size: 1.5rem;
    }

    .receive-quantity {
        font-size: 1rem;
    }

    .modal-xl {
        max-width: 95%;
    }
}